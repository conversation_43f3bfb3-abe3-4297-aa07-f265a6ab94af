import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/provider/audio_img_to_video/audio_case_provider.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_img_to_video.dart';

import '../api.dart';

class AudioImgToVideoService {
  /// 获取对口型案例列表 
  /// caseType
  /// 1:卡通数字人
  /// 2:对口型唱歌
  /// 3:宠物唱歌
  static Future<ApiResponse<List<AudioImgToVideo>>> listLipSyncCase(PageType caseType) async {
    try {
      Map<String, dynamic> data = {};
      switch (caseType) {
        case PageType.syncSinging:
          data = {"caseType": 2};
          break;
        case PageType.petSinging:
          data = {"caseType": 3};
          break;
        case PageType.cartoonDigimon:
          data = {};
          break;
      }
      var response = await HttpUtils.get(Api.listLipSyncCase, params: data);
      BaseResponse<List<AudioImgToVideo>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => AudioImgToVideo.fromJson(list[index]),
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 宠物唱歌
  static Future<ApiResponse<bool>> petSinging({
    required String audioUrl,
    required String imageUrl,
  }) async {
    try {
      var data = {
        "audioUrl": audioUrl,
        "imageUrl": imageUrl,
      };
      var response = await HttpUtils.post(Api.petSinging, data: data);
      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 对口型唱歌
  static Future<ApiResponse<bool>> lipSyncSinging({
    required String audioUrl,
    required String imageUrl,
  }) async {
    try {
      var data = {
        "audioUrl": audioUrl,
        "imageUrl": imageUrl,
      };
      var response = await HttpUtils.post(Api.lipSyncSinging, data: data);
      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

    /// 卡通数字人-音频上传
  static Future<ApiResponse<String?>> UploadAudioFile(String filePath) async {
    try {
      var data = FormData.fromMap({
        "file": await MultipartFile.fromFile(filePath),
      });

      var response = await HttpUtils.post(Api.imgUpload, data: data);

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
