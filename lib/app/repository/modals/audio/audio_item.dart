import 'package:json_annotation/json_annotation.dart';

part 'audio_item.g.dart';

@JsonSerializable()
class AudioItem {
  final String id;
  final String title;
  final String audioUrl;
  final String? cover;
  final Duration? duration;
  final DateTime? createdAt;

  AudioItem({
    required this.id,
    required this.title,
    required this.audioUrl,
    this.cover,
    this.duration,
    this.createdAt,
  });

  factory AudioItem.fromJson(Map<String, dynamic> json) =>
      _$AudioItemFromJson(json);

  Map<String, dynamic> toJson() => _$AudioItemToJson(this);

}
