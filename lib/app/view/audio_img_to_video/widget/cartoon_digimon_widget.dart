import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/widgets/consumption/consumption_display_widget.dart';
import 'package:text_generation_video/app/widgets/image_upload/upload_status_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

class CartoonDigimonWidget extends ConsumerStatefulWidget {
  const CartoonDigimonWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CartoonDigimoWidgetState();
}

class _CartoonDigimoWidgetState extends ConsumerState<CartoonDigimonWidget> {
  Widget _buildUploadImg() {
    final currentModification = ref.watch(photoModificationCurrentProvider);

    return Container(
      margin: const EdgeInsets.fromLTRB(14, 14, 14, 0),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: const Color(0xFF565656), width: 0.6),
      ),
      width: double.infinity,
      height: 450,
      padding: const EdgeInsets.all(6),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: const Color(0xFF191619),
              ),
              child: UploadStatusWidget(
                modification: currentModification,
                defaultChild: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    ref
                        .read(photoModificationCurrentProvider.notifier)
                        .selectImg();
                  },
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(modificationSelectIcon, width: 26),
                      const SizedBox(height: 9),
                      const Text(
                        "上传照片",
                        style:
                            TextStyle(fontSize: 14, color: Color(0xFF8A8D93)),
                      ),
                      const SizedBox(height: 4),
                      const SizedBox(
                        width: 200,
                        child: Text(
                          "仅支持mp4/mov/wmv/wav格式时长不超过2分钟",
                          style:
                              TextStyle(fontSize: 12, color: Color(0xFF8A8D93)),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
                uploadingChild: const Text(
                  "视频上传中\n请耐心等待",
                  style: TextStyle(fontSize: 16, color: Color(0xFFFFFFFF)),
                ),
              ),
            ),
          ),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "示例",
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
              const SizedBox(height: 6),
              _buildSampleImg(
                imgUrl:
                    "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg",
                title: "正脸自拍",
                isError: false,
              ),
              const SizedBox(height: 6),
              _buildSampleImg(
                imgUrl:
                    "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg",
                title: "侧脸",
                isError: true,
              ),
              const SizedBox(height: 6),
              _buildSampleImg(
                imgUrl:
                    "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg",
                title: "面部有干扰",
                isError: true,
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildSampleImg(
      {required String imgUrl, required String title, required bool isError}) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: CachedNetworkImage(
            imageUrl: imgUrl,
            width: 130,
            height: 130,
            fit: BoxFit.cover,
            errorWidget: (_, o, s) {
              return Container(
                width: 130,
                height: 130,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0xFF454348),
                ),
              );
            },
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 25,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withAlpha(100),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          right: 0,
          left: 0,
          bottom: 6,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    isError ? errorExamWhiteIcon : correctExamIcon,
                    width: 18,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    title,
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAudioList() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                "添加配音",
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                 
                },
                child: Row(
                  children: [
                    Image.asset(
                      audioManageIcon,
                      width: 16,
                    ),
                    const SizedBox(width: 6),
                    const Text("管理",
                        style: TextStyle(color: Colors.white, fontSize: 14)),
                  ],
                ),
              )
            ],
          ),
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              childAspectRatio: 1.0,
            ),
            itemCount: 5, // Specify exact count instead of dynamic list
            itemBuilder: (context, index) {
              if (index == 0) {
                return _buildAddAudioItem();
              }

              // Sample audio items
              final audioData = [
                {
                  "url":
                      "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.mp3",
                  "title": "添加配音"
                },
                {
                  "url":
                      "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.mp3",
                  "title": "添加配音"
                },
                {
                  "url":
                      "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.mp3",
                  "title": "02509090909"
                },
                {
                  "url":
                      "https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.mp3",
                  "title": "添加配音添加配音添加配音添加配音添加配音添加配音添加配音添加配音"
                },
              ];

              final audioItem = audioData[index - 1];
              return _buildAudioItem(
                audioUrl: audioItem["url"]!,
                title: audioItem["title"]!,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAddAudioItem() {
    return GestureDetector(
      onTap: () {},
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFF2D2C2F),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFF565656),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomPaint(
              painter: DashedBorderPainter(
                color: const Color(0xFF8A8D93),
                strokeWidth: 1,
                dashLength: 5,
                gapLength: 5,
                radius: 16,
              ),
              child: const SizedBox(
                width: 56,
                height: 56,
                child: Icon(
                  Icons.add,
                  color: Color(0xff8A8D93),
                  size: 30,
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              "录制音频",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioItem({required String audioUrl, required String title}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF565656),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: const Color(0xFF4B4B4B),
              borderRadius: BorderRadius.circular(56),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: SingleChildScrollView(
            padding: EdgeInsets.only(
              top: 10,
              bottom: MediaQuery.paddingOf(context).bottom + 134,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUploadImg(),
                _buildAudioList(),
              ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0x2518161A),
                  Color(0xFF18161A),
                  Color(0xFF18161A)
                ],
                // stops: [0.05, 0.06, 1],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const ConsumptionDisplayWidget(consumption: 20),
                GradientButton(
                  onPress: () {},
                  radius: 16,
                  shadow: false,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  gradient: const LinearGradient(
                    colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                  ),
                  child: const Text(
                    "生成视频",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF18161A),
                    ),
                  ),
                ),
                SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double gapLength;
  final double radius;

  DashedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.gapLength,
    required this.radius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(radius),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0;
      while (distance < pathMetric.length) {
        final segment = pathMetric.extractPath(
          distance,
          distance + dashLength,
        );
        canvas.drawPath(segment, paint);
        distance += dashLength + gapLength;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
